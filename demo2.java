public class demo2 {
    public static void main(String[] args) {
            long startTime = System.currentTimeMillis();

            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < 10000; i++) {
                sb.append("value").append(i);
            }
            String result = sb.toString();
            long endTime = System.currentTimeMillis();
            System.out.println("Result: " + result);
            System.out.println("Time taken: " + (endTime - startTime) + " ms");
        }
}
