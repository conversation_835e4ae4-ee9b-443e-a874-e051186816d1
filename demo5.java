public class demo5 {

    /**
     * 寻找两个正序数组的中位数
     * 时间复杂度: O(log(min(m,n)))
     * 空间复杂度: O(1)
     *
     * @param nums1 第一个正序数组
     * @param nums2 第二个正序数组
     * @return 两个数组的中位数
     */
    public double findMedianSortedArrays(int[] nums1, int[] nums2) {
        // 确保nums1是较短的数组，这样可以优化时间复杂度
        if (nums1.length > nums2.length) {
            int[] temp = nums1;
            nums1 = nums2;
            nums2 = temp;
        }

        int m = nums1.length;
        int n = nums2.length;
        int totalLeft = (m + n + 1) / 2; // 左半部分的元素个数

        // 在nums1中进行二分查找
        int left = 0, right = m;

        while (left < right) {
            int i = left + (right - left + 1) / 2; // nums1中左半部分的元素个数
            int j = totalLeft - i; // nums2中左半部分的元素个数

            // 如果nums1[i-1] > nums2[j]，说明i太大了，需要减小
            if (nums1[i - 1] > nums2[j]) {
                right = i - 1;
            } else {
                left = i;
            }
        }

        int i = left;
        int j = totalLeft - i;

        // 计算左半部分的最大值
        int nums1LeftMax = (i == 0) ? Integer.MIN_VALUE : nums1[i - 1];
        int nums2LeftMax = (j == 0) ? Integer.MIN_VALUE : nums2[j - 1];
        int leftMax = Math.max(nums1LeftMax, nums2LeftMax);

        // 如果总长度是奇数，直接返回左半部分的最大值
        if ((m + n) % 2 == 1) {
            return leftMax;
        }

        // 计算右半部分的最小值
        int nums1RightMin = (i == m) ? Integer.MAX_VALUE : nums1[i];
        int nums2RightMin = (j == n) ? Integer.MAX_VALUE : nums2[j];
        int rightMin = Math.min(nums1RightMin, nums2RightMin);

        // 如果总长度是偶数，返回左半部分最大值和右半部分最小值的平均值
        return (leftMax + rightMin) / 2.0;
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        demo5 solution = new demo5();

        // 测试示例1
        int[] nums1_1 = {1, 3};
        int[] nums2_1 = {2};
        double result1 = solution.findMedianSortedArrays(nums1_1, nums2_1);
        System.out.println("示例1结果: " + result1); // 期望输出: 2.0

        // 测试示例2
        int[] nums1_2 = {1, 2};
        int[] nums2_2 = {3, 4};
        double result2 = solution.findMedianSortedArrays(nums1_2, nums2_2);
        System.out.println("示例2结果: " + result2); // 期望输出: 2.5

        // 额外测试用例
        int[] nums1_3 = {0, 0};
        int[] nums2_3 = {0, 0};
        double result3 = solution.findMedianSortedArrays(nums1_3, nums2_3);
        System.out.println("额外测试结果: " + result3); // 期望输出: 0.0

        // 测试空数组情况
        int[] nums1_4 = {};
        int[] nums2_4 = {1};
        double result4 = solution.findMedianSortedArrays(nums1_4, nums2_4);
        System.out.println("空数组测试结果: " + result4); // 期望输出: 1.0

        // 测试不同长度数组
        int[] nums1_5 = {1, 2};
        int[] nums2_5 = {3, 4, 5, 6};
        double result5 = solution.findMedianSortedArrays(nums1_5, nums2_5);
        System.out.println("不同长度数组测试结果: " + result5); // 期望输出: 3.5
    }
}
