<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="web" name="Web">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/web/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/web" relative="/" />
        </webroots>
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src" isTestSource="false" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="module-library">
      <library>
        <CLASSES>
          <root url="file://$MODULE_DIR$/../../apache-tomcat-8.0.53" />
        </CLASSES>
        <JAVADOC />
        <SOURCES>
          <root url="file://$MODULE_DIR$/../../apache-tomcat-8.0.53/webapps/docs/appdev/sample/src" />
          <root url="file://$MODULE_DIR$/../../apache-tomcat-8.0.53/webapps/examples/WEB-INF/classes" />
          <root url="file://$MODULE_DIR$/../../apache-tomcat-8.0.53/webapps/examples/WEB-INF/jsp/applet" />
          <root url="file://$MODULE_DIR$/../../apache-tomcat-8.0.53/webapps/examples/jsp/plugin/applet" />
          <root url="file://$MODULE_DIR$/../../apache-tomcat-8.0.53/work/Catalina/localhost/ROOT" />
        </SOURCES>
      </library>
    </orderEntry>
  </component>
</module>