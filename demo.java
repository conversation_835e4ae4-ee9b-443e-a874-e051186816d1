public class demo {
    public static void main(String[] args) {
        long startTime = System.currentTimeMillis();

        String result = "";
        for (int i = 0; i < 10000; i++) {
            result += "value" + i;
        }

        long endTime = System.currentTimeMillis();
        System.out.println("Result: " + result);
        System.out.println("Time taken: " + (endTime - startTime) + " ms");
    }
}


