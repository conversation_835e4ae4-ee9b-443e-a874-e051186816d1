import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class demo4 {
    // 给定一个整数数组 nums 和一个整数目标值 target，请你在该数组中找出
    // 和为目标值 target  的那 两个 整数，并返回它们的数组下标。
    public static int[] twoSum(int[] nums, int target) {
        // 创建一个哈希表用于存储数值和对应的索引
        Map<Integer, Integer> map = new HashMap<>();
        // 遍历数组中的每个元素
        for (int i = 0; i < nums.length; i++) {
            // 计算当前元素与目标值的差值
            int complement = target - nums[i];
            // 检查差值是否已存在于哈希表中
            if (map.containsKey(complement)) {
                // 如果存在，则返回当前元素的索引和差值的索引
                return new int[] { map.get(complement), i };
            }
            // 将当前元素及其索引存入哈希表
            map.put(nums[i], i);
        }

        // 如果没有找到解，则抛出异常或返回空数组
        throw new IllegalArgumentException("No two sum solution");
    }

    // 移除重复元素 给你一个 非严格递增排列 的数组 nums ，请你 原地 删除重复出现的元素，使每个元素 只出现一次 ，
    // 返回删除后数组的新长度。元素的 相对顺序 应该保持 一致 。然后返回 nums 中唯一元素的个数。
    public static int removeDuplicates(int[] nums) {
        if (nums == null || nums.length == 0) {
            return 0;
        }

        int i = 0; // 指向唯一元素的最后一个位置

        for (int j = 1; j < nums.length; j++) {
            if (nums[j] != nums[i]) {
                i++;
                nums[i] = nums[j];
            }
        }

        return i+1; // 返回唯一元素的个数
//        int i = 0;
//        for (int n : nums) {
//            if (nums[i] != n) {
//                nums[++i] = n;
//            }
//        }
//        return ++i;
    }
    public static void main(String[] args) {
        demo4 demo4 = new demo4();
        int[] nums = {1, 1, 2, 2, 3, 4, 4, 5};

        int k = demo4.removeDuplicates(nums);
        System.out.println("新长度: " + k);
        System.out.println("修改后的数组: " + Arrays.toString(Arrays.copyOf(nums, k)));
    }
}
